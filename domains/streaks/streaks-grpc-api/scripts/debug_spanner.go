package main

import (
	"context"
	"fmt"
	"log"

	"cloud.google.com/go/spanner"
	"google.golang.org/api/iterator"
)

func main() {
	ctx := context.Background()

	// Connect to the local Spanner emulator using the same path as populate script
	client, err := spanner.NewClient(ctx, "projects/backend-core-dev/instances/bereal-local-instance/databases/entity-post")
	if err != nil {
		log.Fatalf("Failed to create Spanner client: %v", err)
	}
	defer client.Close()

	fmt.Println("✅ Connected to Spanner emulator")
	fmt.Println("\nQuerying posts table directly...")
	fmt.Println("================================")

	// Query posts table
	stmt := spanner.Statement{
		SQL: `SELECT PostID, UserID, MomentID, PostType, PostFormat, CreatedAt FROM posts ORDER BY CreatedAt DESC LIMIT 20`,
	}

	iter := client.Single().Query(ctx, stmt)
	defer iter.Stop()

	fmt.Println("Posts in posts table:")
	postCount := 0
	for {
		row, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			log.Fatalf("Failed to read row: %v", err)
		}

		var postID, userID, momentID string
		var postType, postFormat int64
		var createdAt spanner.NullTime

		if err := row.Columns(&postID, &userID, &momentID, &postType, &postFormat, &createdAt); err != nil {
			log.Fatalf("Failed to parse row: %v", err)
		}

		postCount++
		fmt.Printf("  %d. PostID: %s, UserID: %s, MomentID: %s, Type: %d, Format: %d, CreatedAt: %v\n",
			postCount, postID, userID, momentID, postType, postFormat, createdAt.Time)
	}

	if postCount == 0 {
		fmt.Println("  ⚠️  No posts found in posts table")
	}

	fmt.Println("\nQuerying user_posts table directly...")
	fmt.Println("====================================")

	// Query user_posts table
	stmt2 := spanner.Statement{
		SQL: `SELECT PostID, UserID, MomentID, PostType, PostFormat, CreatedAt FROM user_posts ORDER BY CreatedAt DESC LIMIT 20`,
	}

	iter2 := client.Single().Query(ctx, stmt2)
	defer iter2.Stop()

	fmt.Println("Posts in user_posts table:")
	userPostCount := 0
	for {
		row, err := iter2.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			log.Fatalf("Failed to read row: %v", err)
		}

		var postID, userID, momentID string
		var postType, postFormat int64
		var createdAt spanner.NullTime

		if err := row.Columns(&postID, &userID, &momentID, &postType, &postFormat, &createdAt); err != nil {
			log.Fatalf("Failed to parse row: %v", err)
		}

		userPostCount++
		fmt.Printf("  %d. PostID: %s, UserID: %s, MomentID: %s, Type: %d, Format: %d, CreatedAt: %v\n",
			userPostCount, postID, userID, momentID, postType, postFormat, createdAt.Time)
	}

	if userPostCount == 0 {
		fmt.Println("  ⚠️  No posts found in user_posts table")
	}

	fmt.Printf("\n📊 Summary: %d posts in posts table, %d posts in user_posts table\n", postCount, userPostCount)
	fmt.Println("🎉 Spanner debugging completed!")
}
