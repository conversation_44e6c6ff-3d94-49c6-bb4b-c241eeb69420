package main

import (
	"context"
	"fmt"
	"log"

	gS "cloud.google.com/go/spanner"
)

func main() {
	ctx := context.Background()
	client, err := gS.NewClient(ctx, "projects/backend-core-dev/instances/bereal-local-instance/databases/entity-post")
	if err != nil {
		log.Fatal(err)
	}
	defer client.Close()

	stmt := gS.Statement{SQL: "SELECT COUNT(*) as count FROM posts WHERE UserID = 'user-single-gap'"}
	iter := client.Single().Query(ctx, stmt)
	defer iter.Stop()

	row, err := iter.Next()
	if err != nil {
		log.Fatal(err)
	}
	var count int64
	row.Columns(&count)
	fmt.Printf("Posts count for user-single-gap: %d\n", count)

	// Also check user_posts table
	stmt2 := gS.Statement{SQL: "SELECT COUNT(*) as count FROM user_posts WHERE UserID = 'user-single-gap'"}
	iter2 := client.Single().Query(ctx, stmt2)
	defer iter2.Stop()

	row2, err := iter2.Next()
	if err != nil {
		log.Fatal(err)
	}
	var count2 int64
	row2.Columns(&count2)
	fmt.Printf("user_posts count for user-single-gap: %d\n", count2)
}
