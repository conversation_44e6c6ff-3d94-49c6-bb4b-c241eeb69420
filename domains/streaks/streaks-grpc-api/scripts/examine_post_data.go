package main

import (
	"context"
	"fmt"
	"log"

	gS "cloud.google.com/go/spanner"
)

func main() {
	ctx := context.Background()
	client, err := gS.NewClient(ctx, "projects/backend-core-dev/instances/bereal-local-instance/databases/entity-post")
	if err != nil {
		log.Fatal(err)
	}
	defer client.Close()

	stmt := gS.Statement{SQL: "SELECT PostID, UserID, PostType, PostFormat, MomentID, IsMain, CreatedAt, UpdatedAt, DeletedAt FROM posts WHERE UserID = 'user-single-gap' ORDER BY CreatedAt DESC"}
	iter := client.Single().Query(ctx, stmt)
	defer iter.Stop()

	fmt.Println("Post data for user-single-gap:")
	for {
		row, err := iter.Next()
		if err != nil {
			break
		}
		
		var postID, userID, momentID string
		var postType, postFormat int64
		var isMain bool
		var createdAt, updatedAt interface{}
		var deletedAt interface{}
		
		row.Columns(&postID, &userID, &postType, &postFormat, &momentID, &isMain, &createdAt, &updatedAt, &deletedAt)
		
		fmt.Printf("PostID: %s\n", postID)
		fmt.Printf("UserID: %s\n", userID)
		fmt.Printf("PostType: %d\n", postType)
		fmt.Printf("PostFormat: %d\n", postFormat)
		fmt.Printf("MomentID: %s\n", momentID)
		fmt.Printf("IsMain: %t\n", isMain)
		fmt.Printf("CreatedAt: %v\n", createdAt)
		fmt.Printf("UpdatedAt: %v\n", updatedAt)
		fmt.Printf("DeletedAt: %v\n", deletedAt)
		fmt.Println("---")
	}
}
