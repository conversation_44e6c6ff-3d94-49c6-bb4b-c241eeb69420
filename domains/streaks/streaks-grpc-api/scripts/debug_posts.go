package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	pbPost "github.com/BeReal-App/backend-go/proto/private/post/v2"
)

func main() {
	// Connect to the local post gRPC server
	serverAddr := "localhost:8085" // Post service port

	fmt.Printf("Connecting to post service at %s...\n", serverAddr)

	conn, err := grpc.NewClient(serverAddr, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("Failed to connect to server: %v", err)
	}
	defer conn.Close()

	client := pbPost.NewPostServiceClient(conn)
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	fmt.Println("✅ Connected to post service")
	fmt.Println("\nTesting GetPostsOfUser endpoint...")
	fmt.Println("==================================")

	// Test all users now that we know it works
	testUsers := []string{
		"user-single-gap",
		"user-multiple-gaps",
		"user-perfect-streak",
		"user-too-many-gaps",
	}

	for _, userID := range testUsers {
		fmt.Printf("\n🔍 Testing user: %s\n", userID)

		// Try different request configurations to debug
		fmt.Printf("   🔧 Testing with SearchAll=true (should query posts table)...\n")
		request := &pbPost.GetPostsOfUserRequest{
			UserId: userID,
			Order: &pbPost.OrderBy{
				Field:     pbPost.OrderBy_FIELD_CREATED_AT,
				Direction: pbPost.OrderBy_DIRECTION_DESC,
			},
			IncludeDeleted: func() *bool { b := false; return &b }(),
			SearchAll:      func() *bool { b := true; return &b }(),
			Limit:          100, // Add explicit limit
		}

		resp, err := client.GetPostsOfUser(ctx, request)
		if err != nil {
			fmt.Printf("   ❌ ERROR: %v\n", err)
			continue
		}

		fmt.Printf("   📊 Response Summary:\n")
		fmt.Printf("     - Posts returned: %d\n", len(resp.Posts))
		fmt.Printf("     - PostsWithInteractions: %d\n", len(resp.PostsWithInteractions))

		if len(resp.Posts) > 0 {
			fmt.Printf("   📝 Post Details:\n")
			for i, post := range resp.Posts {
				fmt.Printf("     %d. ID: %s\n", i+1, post.Id)
				fmt.Printf("        UserID: %s\n", post.UserId)
				fmt.Printf("        MomentID: %s\n", post.MomentId)
				fmt.Printf("        CreatedAt: %s\n", post.CreatedAt.AsTime().Format(time.RFC3339))
				fmt.Printf("        Type: %s\n", post.Type.String())
				fmt.Printf("        Format: %s\n", post.Format.String())
				fmt.Printf("        IsMain: %t\n", post.IsMain)
				fmt.Println()
			}
		} else {
			fmt.Printf("   ⚠️  No posts found for user %s\n", userID)
		}

		// Also try with SearchAll=false to test user_posts table
		fmt.Printf("   🔧 Testing with SearchAll=false (should query user_posts table)...\n")
		request2 := &pbPost.GetPostsOfUserRequest{
			UserId: userID,
			Order: &pbPost.OrderBy{
				Field:     pbPost.OrderBy_FIELD_CREATED_AT,
				Direction: pbPost.OrderBy_DIRECTION_DESC,
			},
			IncludeDeleted: func() *bool { b := false; return &b }(),
			SearchAll:      func() *bool { b := false; return &b }(),
			Limit:          100,
		}

		resp2, err := client.GetPostsOfUser(ctx, request2)
		if err != nil {
			fmt.Printf("   ❌ ERROR (SearchAll=false): %v\n", err)
		} else {
			fmt.Printf("   📊 Response Summary (SearchAll=false):\n")
			fmt.Printf("     - Posts returned: %d\n", len(resp2.Posts))
			fmt.Printf("     - PostsWithInteractions: %d\n", len(resp2.PostsWithInteractions))
		}
	}

	fmt.Println("🎉 Post debugging completed!")
}
